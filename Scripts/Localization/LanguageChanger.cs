// Copyright Isto Inc.
using I2.Loc;
using UnityEngine;
using Isto.Core.Enums;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.Core.Localization
{
    public class LanguageChanger : MonoBehaviour
    {
        // Language changer is not editor logic so we can't refer to our editor constants to define our constants
        public const string MENU_PATH = "Isto/Localization/";

        // This is assigned priority as per editor tools wiki
        public const int P_LANGUAGE_MENU = 111;

        // the 10 is creating separator distance for the changer
        // other scripts that add to this menu will start there so we pull back the language changer to allow this
        public const int P_LANGUAGECHANGER = P_LANGUAGE_MENU - 10;

        private const string LANG_CHANGER_SUBMENU_PATH = MENU_PATH + "Language Changer/";

        // These definitions correspond to how our languages are configured in I2Languages.asset
        // We also show these to the user "as is", hence the presence of whitespace... it's a bit annoying
        // TODO: consider using these as internal names (and removing whitespaces)
        // and mapping these internal names onto localized names (and then we can choose if we should actually localize this dropdown and how)
        private static readonly string LANGUAGE_WW = "Wharrgarbl";
        private static readonly string LANGUAGE_EN = "English";
        private static readonly string LANGUAGE_FR = "French";
        private static readonly string LANGUAGE_RU = "Russian";
        private static readonly string LANGUAGE_TW = "Traditional Chinese";
        private static readonly string LANGUAGE_CN = "Simplified Chinese";
        private static readonly string LANGUAGE_KO = "Korean";
        private static readonly string LANGUAGE_JA = "Japanese";
        private static readonly string LANGUAGE_UK = "Ukranian";
        private static readonly string LANGUAGE_DE = "German";
        private static readonly string LANGUAGE_HU = "Hungarian";
        private static readonly string LANGUAGE_TR = "Turkish";
        private static readonly string LANGUAGE_IT = "Italian";
        private static readonly string LANGUAGE_SP = "Spanish";
        private static readonly string LANGUAGE_PT_BR = "Brazilian Portuguese";

        // Can't move this into its own file for now as it's relying on private constants from this file.
        // Should we make them public? If so should the constants be in their own file?
        public class LanguageEnum : StringEnum<LanguageEnum>
        {
            // default and only language if we don't translate the game
            public static readonly LanguageEnum ENGLISH = new LanguageEnum(LANGUAGE_EN);
            public static readonly LanguageEnum FRENCH = new LanguageEnum(LANGUAGE_FR);
            public static readonly LanguageEnum RUSSIAN = new LanguageEnum(LANGUAGE_RU);
            public static readonly LanguageEnum TRADITIONAL_CHINESE = new LanguageEnum(LANGUAGE_TW);
            public static readonly LanguageEnum SIMPLIFIED_CHINESE = new LanguageEnum(LANGUAGE_CN);
            public static readonly LanguageEnum KOREAN = new LanguageEnum(LANGUAGE_KO);
            public static readonly LanguageEnum JAPANESE = new LanguageEnum(LANGUAGE_JA);
            public static readonly LanguageEnum UKRANIAN = new LanguageEnum(LANGUAGE_UK);
            public static readonly LanguageEnum GERMAN = new LanguageEnum(LANGUAGE_DE);
            public static readonly LanguageEnum HUNGARIAN = new LanguageEnum(LANGUAGE_HU);
            public static readonly LanguageEnum TURKISH = new LanguageEnum(LANGUAGE_TR);
            public static readonly LanguageEnum ITALIAN = new LanguageEnum(LANGUAGE_IT);
            public static readonly LanguageEnum SPANISH = new LanguageEnum(LANGUAGE_SP);
            public static readonly LanguageEnum BRAZILIAN_PORTUGUESE = new LanguageEnum(LANGUAGE_PT_BR);

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            // garbage editor-only language for testing fonts and localized strings
            public static readonly LanguageEnum WHARRGARBL = new LanguageEnum(LANGUAGE_WW);
#endif

            public LanguageEnum(string name) : base(name, name)
            {
            }
        }

#if UNITY_EDITOR
        [MenuItem(LANG_CHANGER_SUBMENU_PATH + "Set English", priority = P_LANGUAGECHANGER)]
        public static void TopMenuSetEnglish()
        {
            SetLanguage(LANGUAGE_EN);
        }

        [MenuItem(LANG_CHANGER_SUBMENU_PATH + "Set French", priority = P_LANGUAGECHANGER)]
        public static void TopMenuSetFrench()
        {
            SetLanguage(LANGUAGE_FR);
        }

        [MenuItem(LANG_CHANGER_SUBMENU_PATH + "Set Wharrgarbl", priority = P_LANGUAGECHANGER)]
        public static void TopMenuSetWharrgarbl()
        {
            SetLanguage(LANGUAGE_WW);
        }

        [MenuItem(LANG_CHANGER_SUBMENU_PATH + "Set Russian", priority = P_LANGUAGECHANGER)]
        public static void TopMenuSetRussian()
        {
            SetLanguage(LANGUAGE_RU);
        }

        [MenuItem(LANG_CHANGER_SUBMENU_PATH + "Set Traditional Chinese", priority = P_LANGUAGECHANGER)]
        public static void TopMenuSetTraditionalChinese()
        {
            SetLanguage(LANGUAGE_TW);
        }

        [MenuItem(LANG_CHANGER_SUBMENU_PATH + "Set Simplified Chinese", priority = P_LANGUAGECHANGER)]
        public static void TopMenuSetSimplifiedChinese()
        {
            SetLanguage(LANGUAGE_CN);
        }

        [MenuItem(LANG_CHANGER_SUBMENU_PATH + "Set Korean", priority = P_LANGUAGECHANGER)]
        public static void TopMenuSetKorean()
        {
            SetLanguage(LANGUAGE_KO);
        }

        [MenuItem(LANG_CHANGER_SUBMENU_PATH + "Set Japanese", priority = P_LANGUAGECHANGER)]
        public static void TopMenuSetJapanese()
        {
            SetLanguage(LANGUAGE_JA);
        }

        [MenuItem(LANG_CHANGER_SUBMENU_PATH + "Set Ukranian", priority = P_LANGUAGECHANGER)]
        public static void TopMenuSetUkranian()
        {
            SetLanguage(LANGUAGE_UK);
        }

        [MenuItem(LANG_CHANGER_SUBMENU_PATH + "Set Brazilian Portuguese", priority = P_LANGUAGECHANGER)]
        public static void TopMenuSetBrazilianPortuguese()
        {
            SetLanguage(LANGUAGE_PT_BR);
        }
#endif

        [ContextMenu(nameof(SetEnglish))]
        public void SetEnglish()
        {
            SetLanguage(LANGUAGE_EN);
        }

        [ContextMenu(nameof(SetFrench))]
        public void SetFrench()
        {
            SetLanguage(LANGUAGE_FR);
        }

        [ContextMenu(nameof(SetWharrgarbl))]
        public void SetWharrgarbl()
        {
            SetLanguage(LANGUAGE_WW);
        }

        private static void SetLanguage(string langName)
        {
            Debug.Log("Setting language to " + langName);
            LocalizationManager.CurrentLanguage = langName;
        }

        public static void SetLanguage(LanguageEnum lang)
        {
            SetLanguage(lang.Name);
        }
    }
}