// Copyright Isto Inc.
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AddressableAssets;

namespace Isto.Core.Pooling
{
    /// <summary>
    /// This data asset allows us to define settings for some poolable items.
    /// Note that right now the pools are being only created for the items in this list, but that is not the intended design.
    /// Eventually all poolable items will have pools created for them with default settings, with these definitions overriding
    /// the defaults. But right now the default is "not being created" as we are only partially done implementing pooling for items.
    /// </summary>
    [CreateAssetMenu(fileName = "New PoolingConfigurationSettings", menuName = "Scriptables/Pooling Configuration")]
    public class PoolingConfiguration : ScriptableObject
    {
        public AssetReference coreItemAddressable = default;
        public int coreItemPoolStartingAmount = 20;

        public List<PooledItemConfiguration> ItemPools;

        [Serializable]
        public class PooledItemConfiguration
        {
            public string internalName; // for editor clarity only
            public Item item;
            public int startingAmount;
        }
    }
}