// Copyright Isto Inc.
using Isto.Core.Automation;
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Tiles
{
    public class TilemapCullingController : MonoBehaviour
    {
        public float _cullingSphereRadiusPadding = 30f;

        [Header("Default Tile Setup")]
        public List<DefaultTileSetup> defaultTileSetups;

        private TilemapCullingSettings _tilemapSettings;

        // First key is the culling sphere index with the list being any tilemap with a corner at that position
        private Dictionary<int, List<GameObject>> _tilemapLookup = new Dictionary<int, List<GameObject>>();

        private Vector3 _cornerToCenterOffset = new Vector3();

        [Inject]
        public void Inject(TilemapCullingSettings groundMap)
        {
            _tilemapSettings = groundMap;
        }

        private void OnEnable()
        {
            if (_tilemapSettings == null)
            {
                enabled = false;
                return;
            }

            _cornerToCenterOffset = (Vector3.right + Vector3.forward) * Mathf.CeilToInt(_tilemapSettings.sliceSize / 2f);

            SetupCullingBounds(_tilemapSettings);
        }

        private void OnDestroy()
        {
            if (_cullingGroup != null)
            {
                _cullingGroup.Dispose();
            }
        }

        private void SetupCullingBounds(TilemapCullingSettings groundTileMap)
        {
            int numberOfSubMapsPerRow = Mathf.CeilToInt(_tilemapSettings.tileMapLargestSide / (float)_tilemapSettings.sliceSize);
            float cullingSphereRaidus = GetCullingSphereRadius(_tilemapSettings);

            HashSet<int> alreadySetupSpherePositions = new HashSet<int>();

            _cullingGroup = new CullingGroup();
            _cullingGroup.targetCamera = Camera.main;

            _spheres = new BoundingSphere[numberOfSubMapsPerRow * numberOfSubMapsPerRow];

            int lastUsedSphereIndex = -1;   //Starting at -1 because we increment before using

            for (int i = 0; i < _tilemapSettings.tilemapPrefabs.Count; i++)
            {
                TilemapPrefabSetup setup = _tilemapSettings.tilemapPrefabs[i];

                Vector3 boundingSphereCenter = setup.tilemapCornerPosition + _cornerToCenterOffset;

                int hashedCenter = AutomationGrid.HashCoordinate(Mathf.RoundToInt(boundingSphereCenter.x), Mathf.RoundToInt(boundingSphereCenter.z));

                if (!alreadySetupSpherePositions.Contains(hashedCenter))
                {
                    if (lastUsedSphereIndex >= _spheres.Length)
                    {
                        Debug.LogError($"Past Size of Sphere Array. Center:{boundingSphereCenter}, Corner:{setup.tilemapCornerPosition}, AlreadySeenCount:{alreadySetupSpherePositions.Count}");
                    }

                    lastUsedSphereIndex++;
                    _spheres[lastUsedSphereIndex] = new BoundingSphere(boundingSphereCenter, cullingSphereRaidus);

                    _tilemapLookup[lastUsedSphereIndex] = new List<GameObject>();

                    alreadySetupSpherePositions.Add(hashedCenter);
                }
            }

            _cullingGroup.SetBoundingSpheres(_spheres);
            _cullingGroup.SetBoundingSphereCount(lastUsedSphereIndex + 1);

            _cullingGroup.onStateChanged = CullingGroupStateChanged;
        }

        private float GetCullingSphereRadius(TilemapCullingSettings groundTileMap)
        {
            int halfSliceSize = _tilemapSettings.sliceSize / 2;

            return halfSliceSize + _cullingSphereRadiusPadding;
        }

        private void CullingGroupStateChanged(CullingGroupEvent evt)
        {
            if (evt.hasBecomeInvisible)
            {
                //Debug.Log($"Sphere {evt.index} is now invisible");

                if (_tilemapLookup.ContainsKey(evt.index))
                {
                    for (int i = 0; i < _tilemapLookup[evt.index].Count; i++)
                    {
                        _tilemapLookup[evt.index][i].SetActive(false);
                    }
                }
            }
            else if (evt.hasBecomeVisible)
            {
                if (_tilemapLookup.ContainsKey(evt.index))
                {
                    if (_tilemapLookup[evt.index].Count > 0)
                    {
                        for (int i = 0; i < _tilemapLookup[evt.index].Count; i++)
                        {
                            _tilemapLookup[evt.index][i].SetActive(true);
                        }
                    }
                    else
                    {
                        _tilemapLookup[evt.index] = SpawnTilemapsForArea(_spheres[evt.index].position, evt.index);
                    }
                }
            }
        }

        private List<GameObject> SpawnTilemapsForArea(Vector3 position, int index)
        {
            List<GameObject> createdTilemaps = new List<GameObject>();

            Vector3 corner = position - _cornerToCenterOffset;

            for (int i = 0; i < _tilemapSettings.tilemapPrefabs.Count; i++)
            {
                float distance = Vector3.Distance(_tilemapSettings.tilemapPrefabs[i].tilemapCornerPosition, corner);

                if (Mathf.Abs(distance) < 2f)
                {
                    GameObject spawnedTilemap = Instantiate(_tilemapSettings.tilemapPrefabs[i].prefab, transform);

                    createdTilemaps.Add(spawnedTilemap);
                }
            }

            if (createdTilemaps.Count == 0)
                Debug.LogError($"No tilemaps found for position {position} but it has a bounding sphere setup with index {index}, this shouldn't happen");

            return createdTilemaps;
        }

        // Gizmos

        [SerializeField] private Color _cullingSphereColor = Color.white;
        [SerializeField] private Color _tileAreaColor = Color.white;
        private CullingGroup _cullingGroup;
        private BoundingSphere[] _spheres;

        public void OnDrawGizmosSelected()
        {
            if (Application.isPlaying && enabled)
            {
                for (int i = 0; i < _spheres.Length; i++)
                {
                    if (_spheres[i].radius != 0f)
                    {
                        Gizmos.color = _cullingSphereColor;
                        Gizmos.DrawSphere(_spheres[i].position, GetCullingSphereRadius(_tilemapSettings));
                    }
                }

                DrawDeafultTileAreas();
            }
            else
            {
                if (_tilemapSettings != null)
                {
                    int numberOfSubMapsPerRow = Mathf.CeilToInt(_tilemapSettings.tileMapLargestSide / (float)_tilemapSettings.sliceSize);
                    int sliceSize = _tilemapSettings.sliceSize;
                    int halfSliceSize = _tilemapSettings.sliceSize / 2;

                    float cullingSphereRaidus = GetCullingSphereRadius(_tilemapSettings);

                    Vector3 tileSize = new Vector3(sliceSize, 1f, sliceSize);

                    for (int i = 0; i < numberOfSubMapsPerRow; i++)
                    {
                        for (int j = 0; j < numberOfSubMapsPerRow; j++)
                        {
                            Vector3Int currentCorner = new Vector3Int(_tilemapSettings.bottomCornerPosition.x + (i * sliceSize), 0, _tilemapSettings.bottomCornerPosition.z + (j * sliceSize));
                            Vector3Int sliceCenter = currentCorner + Vector3Int.forward * halfSliceSize + Vector3Int.right * halfSliceSize;

                            Gizmos.color = _cullingSphereColor;
                            Gizmos.DrawWireSphere(sliceCenter, cullingSphereRaidus);

                            Gizmos.color = _tileAreaColor;
                            Gizmos.DrawWireCube(sliceCenter, tileSize);
                        }
                    }
                }

                DrawDeafultTileAreas();
            }
        }

        private void DrawDeafultTileAreas()
        {
            if (defaultTileSetups != null)
            {
                for (int i = 0; i < defaultTileSetups.Count; i++)
                {
                    DefaultTileSetup setup = defaultTileSetups[i];

                    if (setup != null && setup.defaultTile != null)
                    {
                        UnityUtils.DrawBox(setup.bounds, setup.gizmoColor, setup.gizmoColor, setup.gizmoColor, setup.defaultTile.name);
                    }
                }
            }
        }
    }

    [Serializable]
    public class DefaultTileSetup
    {
        public Bounds bounds;
        public GroundRuleTile defaultTile;
        public Color gizmoColor;

        public DefaultTileSetup()
        {
            this.bounds = new Bounds(Vector3.zero, Vector3.left * 10 + Vector3.forward * 10);
            this.gizmoColor = Color.white;
        }
    }
}
