// Copyright Isto Inc.
using FMODUnity;
using UnityEngine;
using Zenject;

namespace Isto.Core.Audio
{
    [RequireComponent(typeof(StudioEventEmitter))]
    public class UISounds : MonoBehaviour
    {
        [EventRef]
        public string buttonErrorRef;
        [EventRef]
        public string buttonHoverRef;

        // Private Variables

#pragma warning disable CS0649
        [SerializeField]
        private StudioEventEmitter _audio;
#pragma warning restore CS0649

        [SerializeField]
        private AudioTrackReferences _tracks;

        private Core.Configuration.Settings _settings;

        // Lifecycle Events

        [Inject]
        public void Inject(Core.Configuration.Settings settings)
        {
            _settings = settings;
        }

        public void PlayButtonClickSound()
        {
            if (!string.IsNullOrEmpty(_audio.Event))
            {
                _audio.Play();
            }
        }

        public void PlayButtonHoverSound()
        {
            PlayOneShot(buttonHoverRef);
        }

        public void PlayButtonErrorSound()
        {
            PlayOneShot(buttonErrorRef);
        }

        public void PlayOneShot(string path)
        {
            RuntimeManager.PlayOneShot(path, transform.position);
        }

        public void PlayOneShot(EventReference eventRef)
        {
#if !CLOUD_BUILD
            RuntimeManager.PlayOneShot(eventRef);
#endif
        }
    }
}