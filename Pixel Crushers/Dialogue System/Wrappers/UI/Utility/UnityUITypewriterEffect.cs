// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem.Wrappers
{

    /// <summary>
    /// This wrapper class keeps references intact if you switch between the 
    /// compiled assembly and source code versions of the original class.
    /// </summary>
    [HelpURL("https://pixelcrushers.com/dialogue_system/manual2x/html/dialogue_u_is.html#dialogueUITypewriterEffect")]
    [AddComponentMenu("Pixel Crushers/Dialogue System/UI/Unity UI/Effects/Unity UI Typewriter Effect")]
    [DisallowMultipleComponent]
    public class UnityUITypewriterEffect : PixelCrushers.DialogueSystem.UnityUITypewriterEffect
    {
    }

}
