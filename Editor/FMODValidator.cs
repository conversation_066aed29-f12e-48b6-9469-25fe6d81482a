//Copyrite Isto Inc. 2018
using FMODUnity;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEditor.Build;
using UnityEngine;

namespace Isto.Core
{
	public static class FMODValidator 
	{
        public static bool AreFMODEventRefsValid(GameObject[] rootObjects)
        {
            bool allValid = true;

            foreach (GameObject gameObject in rootObjects)
            {
                MonoBehaviour[] allBehvaiours = gameObject.GetComponentsInChildren<MonoBehaviour>();

                for (int i = 0; i < allBehvaiours.Length; i++)
                {
                    if(allBehvaiours[i] != null)
                    {
                        Type componentType = allBehvaiours[i].GetType();

                        FieldInfo[] fields = componentType.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

                        foreach (FieldInfo item in fields)
                        {
                            if (item.FieldType == typeof(EventReference))
                            {
                                EventReference er = (EventReference)item.GetValue(allBehvaiours[i]);
                                string output = er.Path;

                                if (er.IsNull || !IsValidEventRef(output))
                                {
                                    Debug.LogError($"Unable to find FMOD Event {output} on object of type {componentType.ToString()} from gameobject {allBehvaiours[i].gameObject.name}");
                                    allValid = false;
                                }
                            }
                            else if (HasAttribute(item, typeof(EventRefAttribute)))
                            {
                                if (item.FieldType == typeof(string))
                                {
                                    string output = item.GetValue(allBehvaiours[i]) as string;

                                    if (!IsValidEventRef(output))
                                    {
                                        Debug.LogError($"Unable to find FMOD Event {output} on object of type {componentType.ToString()} from gameobject {allBehvaiours[i].gameObject.name}");
                                        allValid = false;
                                    }
                                }
                                else if (typeof(IEnumerable).IsAssignableFrom(item.FieldType))
                                {
                                    foreach (var listItem in (IEnumerable)item.GetValue(allBehvaiours[i]))
                                    {
                                        if (listItem.GetType() == typeof(string))
                                        {
                                            string listOutput = listItem as string;

                                            if (!IsValidEventRef(listOutput))
                                            {
                                                Debug.LogError($"Unable to find FMOD Event {listOutput} on object of type {componentType.ToString()} from gameobject {allBehvaiours[i].gameObject.name}");
                                                allValid = false;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return allValid;
        }

        public static bool AreFMODEventRefsValid(ScriptableObject[] assets)
        {
            bool allValid = true;

            foreach (ScriptableObject asset in assets)
            {
                Type assetType = asset.GetType();

                FieldInfo[] fields = assetType.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

                allValid &= FindAndValidateEventRefs(fields, asset);
            }

            return allValid;
        }

        private static bool FindAndValidateEventRefs(FieldInfo[] infos, object valueObject)
        {
            bool allValid = true;

            foreach (FieldInfo item in infos)
            {
                if (HasAttribute(item, typeof(EventRefAttribute)))
                {
                    if (item.FieldType == typeof(string))
                    {
                        string output = item.GetValue(valueObject) as string;

                        if (!IsValidEventRef(output))
                        {
                            Debug.LogError($"Unable to find FMOD Event {output} on object {valueObject.ToString()}");
                            allValid = false;
                        }
                    }
                    else if (typeof(IEnumerable).IsAssignableFrom(item.FieldType))
                    {
                        foreach (var listItem in (IEnumerable)item.GetValue(valueObject))
                        {
                            if (listItem.GetType() == typeof(string))
                            {
                                string listOutput = listItem as string;

                                if (!IsValidEventRef(listOutput))
                                {
                                    Debug.LogError($"Unable to find FMOD Event {listOutput} on object {valueObject.ToString()}");
                                    allValid = false;
                                }
                            }
                        }
                    }
                }
            }

            return allValid;
        }

        /// <summary>
        /// Checks if the passed in object has any of the specified attributes. 
        /// Code based on Zenject TypeExtensions.cs file.
        /// </summary>
        /// <param name="provider"></param>
        /// <param name="attributeTypes"></param>
        /// <returns></returns>
        private static bool HasAttribute(MemberInfo provider, params Type[] attributeTypes)
        {
            Attribute[] allAttributes = Attribute.GetCustomAttributes(provider, typeof(Attribute), true);

            if (allAttributes.Length == 0)
                return false;

            return allAttributes.Where(a => attributeTypes.Any(x => a.GetType() == x || x.IsAssignableFrom(a.GetType()))).Any();
        }

        /// <summary>
        /// Checks FMODs EventManager to see if the passed in reference can be found.  Returns true if an empty reference is passed in.
        /// </summary>
        /// <param name="reference"></param>
        /// <returns></returns>
        public static bool IsValidEventRef(string reference)
        {
            if (string.IsNullOrEmpty(reference)) return true;

            EditorEventRef eventRef = EventManager.EventFromPath(reference);

            return eventRef != null;
        }
    }
}