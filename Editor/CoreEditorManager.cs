// Copyright Isto Inc.

using System.Linq;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;
using Zenject;
using Zenject.Internal;

namespace Isto.Core
{
    /// <summary>
    /// Keeps our contracted scenes loaded together even in editor for convenience.
    /// </summary>
    [InitializeOnLoad]
    [DefaultExecutionOrder(100)]
    public static class CoreEditorManager
    {
        /// <summary>
        /// Allows to turn off the automated scene contract loader logic in case you wanted to open a single scene
        /// without its parent contract (e.g. during scripted validation logic)
        /// </summary>
        public static bool Active = true;
        
        static CoreEditorManager()
        {
            EditorSceneManager.sceneOpened -= OnSceneOpened;
            EditorSceneManager.sceneOpened += OnSceneOpened;
        }
        
        private static void OnSceneOpened(UnityEngine.SceneManagement.Scene scene, OpenSceneMode mode)
        {
            if (!Active) return;

            Debug.Log("CoreEditorManager: Scene Opened.  Name:" + scene.name);

            // If only one scene open, check if SceneContext has contract for a parent scene
            if(EditorSceneManager.sceneCount == 1)
            {
                SceneContext context = FindSceneContext(scene);

                if(context != null)
                {
                    string parentContract = context.ParentContractNames.FirstOrDefault();

                    if (!string.IsNullOrEmpty(parentContract))
                    {
                        Debug.Log("CoreEditorManager: Attempt to load contract scene.  Name:" + parentContract);
                        LoadParentScene(parentContract, scene);

                        SceneManager.SetActiveScene(scene);
                    }
                }
            }
        }
        
        private static void LoadParentScene(string parentContract, Scene mainScene)
        {
            DefaultSceneContractConfig[] configs = Resources.LoadAll<Zenject.Internal.DefaultSceneContractConfig>(DefaultSceneContractConfig.ResourcePath);
            
            foreach (var config in configs)
            {
                foreach (var info in config.DefaultContracts)
                {
                    if (info.ContractName == parentContract)
                    {
                        Scene additiveScene = EditorSceneManager.OpenScene(AssetDatabase.GetAssetPath(info.Scene), OpenSceneMode.Additive);
                        EditorSceneManager.MoveSceneBefore(additiveScene, mainScene);
                    }
                }
            }
        }
        
        private static SceneContext FindSceneContext(Scene scene)
        {
            GameObject[] rootObjects = scene.GetRootGameObjects();

            for (int i = 0; i < rootObjects.Length; i++)
            {
                SceneContext context = rootObjects[i].GetComponent<SceneContext>();

                if (context != null)
                    return context;
            }

            return null;
        }
    }
}