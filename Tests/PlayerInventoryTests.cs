// Copyright Isto Inc.
using Isto.Core.Beings;
using Isto.Core.Items;
using NUnit.Framework;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Tests
{
    [Category("Player Inventory Tests")]
    public class PlayerInventoryTests
    {
#if UNITY_EDITOR
        private PlayerInventory CreatePlayerInventory()
        {
            GameObject dummy = new GameObject();
            
            PlayerInventory container = dummy.AddComponent<PlayerInventory>();
            container.startingItems = ScriptableObject.CreateInstance(typeof(ItemPileList)) as ItemPileList;
            container.startingItems.itemPiles = new List<ItemPile>();
            container.startingEquippedItems = ScriptableObject.CreateInstance(typeof(ItemList)) as ItemList;
            container.startingEquippedItems.items = new List<CoreItem>(2);
            container.startingEquippedItems.items.Add(ScriptableObject.CreateInstance(typeof(CoreItem)) as CoreItem);
            container.startingEquippedItems.items.Add(ScriptableObject.CreateInstance(typeof(CoreItem)) as CoreItem);
            
            // Normally the gameobject needs Awake to run for initialization and this would only work in playmode.
            // We can circumvent all that and run the test without playmode by just executing the important part here.
            // Might not be ideal, but this item controller is already pretty artificial.
            container.InitializeList();

            return container;
        }

        // Note that in the Core module we've created example items that could be used here if we wanted to avoid
        // making new ones here. See CraftingQueueTests for reference.
        private CoreItem GetCraftableItem()
        {
            CoreItem newItem = ScriptableObject.CreateInstance<CoreItem>();
            CoreItem ingredientOne = ScriptableObject.CreateInstance<CoreItem>();
            ingredientOne.itemID = "ItemOne";
            CoreItem ingredientTWo = ScriptableObject.CreateInstance<CoreItem>();
            ingredientTWo.itemID = "ItemTwo";

            Recipe tempRecipe = ScriptableObject.CreateInstance<Recipe>();
            tempRecipe.inputs.Clear();
            tempRecipe.inputs.Add(new ItemPile(ingredientOne, 2));
            tempRecipe.inputs.Add(new ItemPile(ingredientTWo, 3));

            newItem.SetRecipe(tempRecipe);

            return newItem;
        }

        [Test]
        public void PlayerInventoryTests_Add_Item()
        {
            PlayerInventory container = CreatePlayerInventory();

            CoreItem testItem = ScriptableObject.CreateInstance(typeof(CoreItem)) as CoreItem;

            int successDeposited = container.Add(testItem);

            Assert.AreEqual(container.Items[0].item, testItem);
            Assert.AreEqual(container.Items[0].count, 1);
            Assert.AreEqual(1, successDeposited);
            Assert.AreEqual(container.pileCount, container.Items.Count);
        }

        [Test]
        public void PlayerInventoryTests_Get_Item_Count()
        {
            PlayerInventory container = CreatePlayerInventory();

            CoreItem testItem = ScriptableObject.CreateInstance(typeof(CoreItem)) as CoreItem;
            testItem.itemID = "ItemOne";
            CoreItem testItemTwo = ScriptableObject.CreateInstance(typeof(CoreItem)) as CoreItem;
            testItemTwo.itemID = "ItemTwo";

            container.Add(testItem, 30);
            container.Add(testItemTwo, 14);

            int countOne = container.GetCountOfItem(testItem);
            int countTwo = container.GetCountOfItem(testItemTwo);

            Assert.AreEqual(30, countOne);
            Assert.AreEqual(14, countTwo);

            container.Remove(testItem, 5);

            Assert.AreEqual(25, container.GetCountOfItem(testItem));
            Assert.AreEqual(14, container.GetCountOfItem(testItemTwo));

            container.Remove(testItemTwo, 14);

            Assert.AreEqual(0, container.GetCountOfItem(testItemTwo));
        }

        [Test]
        public void PlayerInventoryTests_Has_Ingredients_Check()
        {
            PlayerInventory inventory = CreatePlayerInventory();
            CoreItem craftableItem = GetCraftableItem();

            for (int i = 0; i < craftableItem.GetRecipe().inputs.Count; i++)
            {
                inventory.Add(craftableItem.GetRecipe().inputs[i]);
            }

            bool hasIngredients = inventory.HasIngredients(craftableItem);

            Assert.IsTrue(hasIngredients);
        }

        [Test]
        public void PlayerInventoryTests_Doesnt_Have_Ingredients_Check()
        {
            PlayerInventory inventory = CreatePlayerInventory();
            CoreItem craftableItem = GetCraftableItem();

            for (int i = 0; i < craftableItem.GetRecipe().inputs.Count; i++)
            {
                inventory.Add(craftableItem.GetRecipe().inputs[i].item, craftableItem.GetRecipe().inputs[i].count - 1);
            }

            bool hasIngredients = inventory.HasIngredients(craftableItem);

            Assert.IsFalse(hasIngredients);
        }
#endif
    }
}
