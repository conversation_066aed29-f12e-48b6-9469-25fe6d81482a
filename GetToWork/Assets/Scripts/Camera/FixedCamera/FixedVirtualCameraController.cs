using System;
using Cinemachine;
using Isto.Core.Inputs;
using Isto.GTW.Player;
using UnityEngine;
using Zenject;

namespace Isto.GTW.Camera
{
    public class FixedVirtualCameraController : MonoBehaviour
    {
        // UNITY HOOKUPS

        [SerializeField] private int _activatedCameraPriority = 20;
        [SerializeField] private float _cameraSensitivity = 0.5f;
        [SerializeField] private float _rotationResetTime = 1f;
        
        
        // OTHER FIELDS

        private FixedVirtualCamera _activatedFixedVirtualCamera = null;
        private Vector3 _currentRotation = Vector3.zero;
        private Vector3 _currentRotationVel = Vector3.zero;
        private float _autoRotateResetTimeLeft = -1f;


        // PROPERTIES
        
        PlayerVirtualCameraController PlayerVirtualCameraController => _playerController.PlayerVirtualCameraController;
        
        public FixedVirtualCamera ActivatedFixedVirtualCamera => _activatedFixedVirtualCamera;
        public bool IsFixedCameraActivated => ActivatedFixedVirtualCamera != null;
        
        
        // INJECTION
        
        protected PlayerController _playerController;
        protected CinemachineBrain _cinemachineBrain;
        
        [Inject]
        private void Inject(PlayerController playerController, CinemachineBrain cinemachineBrain)
        {
            _playerController = playerController;
            _cinemachineBrain = cinemachineBrain;
        }
        
        
        // UNITY LIFECYCLE

        private void Update()
        {
            UpdateFixedCameraRotation(_activatedFixedVirtualCamera);
        }


        // OTHER METHODS

        private void UpdateFixedCameraRotation(FixedVirtualCamera fixedVirtualCamera)
        {
            if (fixedVirtualCamera == null)
            {
                return;
            }
            
            _autoRotateResetTimeLeft -= Time.deltaTime;

            Vector2 cameraInput = PlayerVirtualCameraController.CameraInput;
            float verticalSpeed = PlayerVirtualCameraController.UsingController ? 0.5f : 1f;

            if (PlayerVirtualCameraController.HasMouseInput)
            {
                _autoRotateResetTimeLeft = 1f;
            }
            else if (PlayerVirtualCameraController.HasControllerInput)
            {
                _autoRotateResetTimeLeft = 1f;
            }

            float sensitivity = _cameraSensitivity * PlayerVirtualCameraController.InputRotateSpeedMultiplier;
            _currentRotation.y += cameraInput.x * sensitivity * Time.deltaTime;
            _currentRotation.x += -cameraInput.y * sensitivity * Time.deltaTime * verticalSpeed;
        
            _currentRotation.y = Mathf.Clamp(_currentRotation.y, fixedVirtualCamera.DefaultRotation.y - fixedVirtualCamera.HorizontalLimits, fixedVirtualCamera.DefaultRotation.y + fixedVirtualCamera.HorizontalLimits);
            _currentRotation.x = Mathf.Clamp(_currentRotation.x, fixedVirtualCamera.DefaultRotation.x - fixedVirtualCamera.VerticalLimits, fixedVirtualCamera.DefaultRotation.x + fixedVirtualCamera.VerticalLimits);
            
            if (_autoRotateResetTimeLeft < 0f)
            {
                _currentRotation = Vector3.SmoothDamp(_currentRotation,
                    fixedVirtualCamera.DefaultRotation,
                    ref _currentRotationVel,
                    _rotationResetTime,
                    float.MaxValue,
                    Time.deltaTime);
            }
            else
            {
                _currentRotationVel = Vector3.zero;
            }

            fixedVirtualCamera.transform.localRotation = Quaternion.Euler(_currentRotation);
        }

        public void ActivateVirtualCamera(FixedVirtualCamera virtualCamera)
        {
            DeactivateVirtualCamera();

            _cinemachineBrain.m_DefaultBlend.m_Time = 0f;
            _activatedFixedVirtualCamera = virtualCamera;
            _activatedFixedVirtualCamera.ResetCameraRotation();
            _currentRotation = virtualCamera.DefaultRotation;
            virtualCamera.VirtualCamera.m_Priority = _activatedCameraPriority;
        }
        
        public void DeactivateVirtualCamera(FixedVirtualCamera virtualCamera)
        {
            if (_activatedFixedVirtualCamera != virtualCamera)
            {
                return;
            }

            DeactivateVirtualCamera();
        }
        
        public void DeactivateVirtualCamera()
        {
            if (_activatedFixedVirtualCamera == null)
            {
                return;
            }
            
            _cinemachineBrain.m_DefaultBlend.m_Time = 1f;
            _activatedFixedVirtualCamera.VirtualCamera.m_Priority = 0;
            
            SetPlayerCamera(_activatedFixedVirtualCamera.transform.position);
            _activatedFixedVirtualCamera = null;
        }

        private void SetPlayerCamera(Vector3 cameraPosition)
        {
            Vector3 toPlayer = _playerController.PlayerVirtualCameraController.AimTarget.position - cameraPosition;
            Vector3 toPlayerFlat = new Vector3(toPlayer.x, 0f, toPlayer.z);
            Vector3 toPlayerSide = Vector3.Cross(toPlayer, toPlayerFlat);

            float horizontalAngle = Vector3.SignedAngle(Vector3.forward, toPlayerFlat, Vector3.up);
            float verticalAngle = Vector3.SignedAngle(toPlayerFlat, toPlayer, toPlayerSide);
            
            _playerController.PlayerVirtualCameraController.SetCameraRotation(new Vector2(verticalAngle, horizontalAngle));
            _playerController.PlayerVirtualCameraController.SetRotationInputBlockTime(1f);
        }
    }
}