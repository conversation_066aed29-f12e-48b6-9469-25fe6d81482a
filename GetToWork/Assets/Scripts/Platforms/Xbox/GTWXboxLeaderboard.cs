// Copyright Isto Inc.

using Isto.GTW.Data;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.GTW.Leaderboards
{
    public class GTWXboxLeaderboard : MonoBehaviour, ILeaderboard
    {
        // UNITY HOOKUP

        [Header("Steamworks Leaderboard Name")]
        [SerializeField]
        private string _leaderboardName = "DoinklerSpecial_HighestCheckpoint";


        // OTHER FIELDS



        // EVENTS

        public event Action<List<LeaderboardEntryData>> OnCheckpointsDownloaded;


        // OTHER METHODS

        public void UploadHighestCheckpoint(int score)
        {
            // TODO: Save this to PlayerPrefs
        }

        public void DownloadFriendsEntries(int startRank, int endRank)
        {
            // // TODO: Load some dummy data and organize it based on rank
            // var data = new LeaderboardEntryData(
            //     entry.m_steamIDUser,
            //     playerName,
            //     entry.m_nScore,
            //     globalRank,
            //     localRank
            // );
            // entries.Add(data);
            //
            //
            // OnCheckpointsDownloaded?.Invoke(entries);
            // Debug.Log($"[LeaderboardManager] Requesting entries {startRank}–{endRank}...");
        }
    }
}