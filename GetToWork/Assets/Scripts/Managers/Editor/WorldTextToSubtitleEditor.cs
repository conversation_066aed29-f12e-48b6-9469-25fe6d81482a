using UnityEditor;
using UnityEngine;

namespace Isto.GTW.Managers.Editor
{
    [CustomEditor(typeof(WorldTextToSubtitle))]
    public class WorldSubtitleEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            WorldTextToSubtitle instance = target as WorldTextToSubtitle;
            
            if (GUILayout.Button("Spawn subtitles"))
            {
                instance.SpawnWorldSubtitle(force: true);
            }
        }
    }
}