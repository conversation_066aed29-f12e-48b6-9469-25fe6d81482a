// Copyright Isto Inc.
using Isto.Core.Achievements;
using Isto.Core.Analytics;
using Isto.Core.Configuration;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.Installers;
using Isto.Core.Platforms;
using Isto.Core.Scenes;
using Isto.Core.Skins;
using Isto.Core.Speedrun;
using Isto.Core.Themes;
using Isto.Core.UI;
using Isto.GTW.Providers;
using Isto.GTW.Leaderboards;
using Rewired.Integration.UnityUI;
using UnityEngine;

#if PLATFORM_GAMECORE && !UNITY_EDITOR
using Isto.Core.Platforms.Xbox;
#endif

namespace Isto.GTW
{
    public class ProjectInstaller : CoreProjectInstaller
    {
        [Header("Get to Work Specifics")]
        public GameObject xboxGameDataManagerPrefab;
        public GameObject DoinklerSpecialLeaderboardManagerPrefab;
        // public GameObject developerConsolePrefab;

        [Header("Platform Tools")]
        public uint steamStoreID = 0; // 2706170 for GTW

        public override void InstallBindings()
        {
            Container.Bind<GTWInitialization>().FromComponentInHierarchy().AsSingle();

            Container.Bind<IControls>().To<Controls>().FromComponentInNewPrefab(controlsPrefab).AsSingle();
            Container.Bind<GameScenesReference>().FromInstance(coreGameScenesReference).AsSingle();

            //Adding controls as a single since only one should ever be needed
            Container.Bind<RewiredStandaloneInputModule>().FromComponentInNewPrefab(eventSystemPrefab).AsSingle().NonLazy();
            Container.Bind<Rewired.InputManager>().FromComponentInNewPrefab(rewiredPrefab).AsSingle().NonLazy();
            Container.Bind<UserKeybindingDataStore>().FromComponentInHierarchy().AsSingle();
            Container.Bind<ControllerGlyphs>().FromComponentInNewPrefab(glyphsPrefab).AsSingle();

            Container.Bind<Settings>().FromResource("GTWDefaultSettings");
            Container.Bind(typeof(SpeedrunSettings)).FromResource(SpeedrunSettings.ASSET_NAME).AsCached();

            Container.Bind(typeof(GTWGameState), typeof(GameState)).FromComponentInNewPrefab(gameStatePrefab).AsSingle().NonLazy();

            // Audio Bindings
            InstallAudio();

            // Data Management
            string gameDataManagerName = "Game Data";
            //Container.Bind<IGameData>().FromComponentInNewPrefab(pcGameDataManagerPrefab).WithGameObjectName(gameDataManagerName).AsSingle().NonLazy();
            Container.Bind<string>().WithId("SaveFileSuffix").FromInstance(fileNameSufix).AsSingle();
            Container.Bind<IGameData.GameDataLocation>().WithId("SaveSlotLocation").FromInstance(saveSlotLocation).AsSingle();

            // Our XBox user data management code will exclusively work on the console, so we can't test it in editor
#if PLATFORM_GAMECORE && !UNITY_EDITOR
            Container.Bind<IGameData>().FromComponentInNewPrefab(xboxGameDataManagerPrefab).WithGameObjectName(gameDataManagerName).AsSingle().NonLazy();
#else
            Container.Bind<IGameData>().FromComponentInNewPrefab(pcGameDataManagerPrefab).WithGameObjectName(gameDataManagerName).AsSingle().NonLazy();
#endif


            Container.Bind(typeof(IAnalyticsHandler)).FromComponentInNewPrefab(analyticsManagerPrefab).WithGameObjectName("Analytics Manager").AsSingle().NonLazy();


            Container.Bind<UIModalPopup>().FromComponentInNewPrefab(uiErrorPopupPrefab).WithGameObjectName("Error Dialog").AsSingle().NonLazy();
            Container.Bind<UIModalChoicePopup>().FromComponentInNewPrefab(uiChoicePopupPrefab).WithGameObjectName("Choice Dialog").AsSingle().NonLazy();
            Container.Bind<ThemeManager>().FromComponentInNewPrefab(themeManagerPrefab).WithGameObjectName("Theme Manager").AsSingle().NonLazy();

            // Doinkler Specific

            ConfigureDoinklerBindings();

            // Themes

            Container.Bind<ThemeSetup>().FromInstance(themeSetup).AsSingle();

            // Skins

            Container.Bind<MasterSkinList>().FromInstance(masterSkinList).AsSingle();

            //Get to Work Specifics

            // Platforms
            //Container.Bind<IPlatformTools>().FromInstance(new EmptyPlatformTools()).AsSingle();

#if PLATFORM_STEAM
            gameObject.AddComponent<SteamManager>();
            Container.Bind<IPlatformTools>().FromInstance(new SteamPlatformTools(steamStoreID)).AsSingle();
            SteamAchievements achievementsInstance = new SteamAchievements();
            //Container.Bind(typeof(IAchievements), typeof(IGTWAchievements)).FromMethod(context => achievementsInstance).AsSingle().NonLazy();
            Container.Bind<ILeaderboard>().FromComponentInNewPrefab(DoinklerSpecialLeaderboardManagerPrefab).WithGameObjectName("Doinkler Leaderboard Manager").AsSingle().NonLazy();
            Container.Bind(typeof(IAchievements)).FromMethod(context => achievementsInstance).AsSingle().NonLazy();
            Container.QueueForInject(achievementsInstance);
#elif PLATFORM_GAMECORE && !UNITY_EDITOR
            // Let Zenject construct our objects so injection happens
            Container.Bind<IPlatformTools>().To<XboxPlatformTools>().FromResolve();
            Container.Bind<XboxPlatformTools>().FromNewComponentOnNewGameObject().WithGameObjectName("XboxPlatformTools").AsSingle();
            Container.Bind(typeof(IAchievements)).To<XboxAchievements>().FromResolve();
            Container.Bind<XboxAchievements>().FromNew().AsSingle();
#else
            Container.Bind<IPlatformTools>().FromInstance(new EmptyPlatformTools()).AsSingle();
            //Container.Bind(typeof(IAchievements), typeof(IGTWAchievements)).FromInstance(new GTWDisabledAchievements()).AsSingle();
            Container.Bind(typeof(IAchievements)).FromInstance(new DisabledAchievements()).AsSingle();
#endif

            InstallLanguages();
            InstallDevConsole();
        }

        private void ConfigureDoinklerBindings()
        {
            Container.Bind<IDoinklerWorldDefinitionProvider>().To<DoinklerWorldDefinitionProvider>().AsSingle();
        }
    }
}