// Copyright Isto Inc.

using Isto.Core.Speedrun;
using Isto.GTW.Configuration;
using Isto.GTW.Managers;
using Isto.GTW.Player;
using Isto.GTW.Providers;
using Isto.GTW.UI;
using UnityEngine;
using Zenject;

namespace Isto.GTW.Installers
{
    public class DoinklerSceneInstaller : MonoInstaller<DoinklerSceneInstaller>
    {
        [Inject]
        private PlayerController _playerController;

        [SerializeField] private GTWUINextLevelPopup _uiNextLevelPopup;
        [SerializeField] GTWGameWorldDefinition _sceneDefinition;


        public override void InstallBindings()
        {
            Container.Bind<GTWUINextLevelPopup>().FromComponentInNewPrefab(_uiNextLevelPopup).WithGameObjectName("Next Level Popup").AsSingle().NonLazy();
            Container.Bind<GTWDoinklerLevelTimeManager>().FromNewComponentOnNewGameObject().WithGameObjectName("Level Time Manager").AsSingle().NonLazy();
            Container.Resolve<IDoinklerWorldDefinitionProvider>().Current = _sceneDefinition;
        }

        private void Awake()
        {
            _playerController.restartAbilityAvailable = true;
        }
    }
}