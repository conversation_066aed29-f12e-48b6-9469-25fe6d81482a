// Copyright Isto Inc.

using Steamworks;

namespace Isto.GTW.Data
{
    public class LeaderboardEntryData
    {
        public CSteamID SteamID { get; set; }
        public string PlayerName { get; set; }
        public int HighestCheckpoint { get; set; }
        public int GlobalRank { get; set; }
        public int LocalRank { get; set; }

        public LeaderboardEntryData(CSteamID steamID, string playerName, int highestCheckpoint, int globalRank, int localRank)
        {
            SteamID = steamID;
            PlayerName = playerName;
            HighestCheckpoint = highestCheckpoint;
            GlobalRank = globalRank;
            LocalRank = localRank;
        }
    }
}