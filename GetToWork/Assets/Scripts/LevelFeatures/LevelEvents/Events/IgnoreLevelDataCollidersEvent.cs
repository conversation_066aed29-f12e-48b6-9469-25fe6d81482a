// Copyright Isto Inc.

using Isto.GTW.Player;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.GTW.LevelFeatures.LevelEvents
{
    public class IgnoreLevelDataCollidersEvent : LevelEvent
    {
        // UNITY HOOKUPS

        [SerializeField] private List<string> _sceneNamesToIgnore = new List<string>();


        // INJECTION

        private PlayerController _playerController;

        [Inject]
        public void Inject(PlayerController playerController)
        {
            _playerController = playerController;
        }


        // OTHER METHODS

        public override void TriggerEvent(bool initialization = false)
        {
            _playerController.SetIgnoreLevelDataColliders(_sceneNamesToIgnore);
        }
    }
}