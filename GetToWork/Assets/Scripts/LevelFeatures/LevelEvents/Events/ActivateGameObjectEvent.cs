// Copyright Isto Inc.

using System.Collections.Generic;
using UnityEngine;

namespace Isto.GTW.LevelFeatures.LevelEvents
{
    public class ActivateGameObjectEvent : LevelEvent
    {
        // UNITY HOOKUPS

        [SerializeField] private List<GameObject> _gameObjectsToActivate = new List<GameObject>();
        [SerializeField] private List<GameObject> _gameObjectsToDeactivate = new List<GameObject>();

        [Space]
        [SerializeField] private bool _initializeOnAwake = true;

        // OTHER METHODS

        protected override void Awake()
        {
            base.Awake();

            if (_initializeOnAwake)
            {
                SetGameObjectActivation(false);
            }
        }

        private void SetGameObjectActivation(bool isActive)
        {
            foreach (GameObject go in _gameObjectsToActivate)
            {
                if (go.activeSelf != isActive)
                {
                    go.SetActive(isActive);
                }
            }

            foreach (GameObject go in _gameObjectsToDeactivate)
            {
                if (go.activeSelf == isActive)
                {
                    go.SetActive(!isActive);
                }
            }
        }

        public override void TriggerEvent(bool initialization = false)
        {
            SetGameObjectActivation(true);
        }
    }
}