// Copyright Isto Inc.

using UnityEngine;

namespace Isto.GTW.LevelFeatures
{
    public abstract class LevelEvent : MonoBehaviour
    {
        // UNITY HOOKUPS

        [SerializeField] private LevelEventTrigger _levelEventTrigger;
        [SerializeField] private bool _triggerOnce;
        [SerializeField] private bool _wasTriggered;

        public bool IsTriggerEventValid => !(_triggerOnce && _wasTriggered);

        protected virtual void Awake()
        {
            if (_levelEventTrigger != null)
            {
                _levelEventTrigger.OnTriggerEvent += LevelEventTrigger_OnTriggerEvent;
            }
        }


        // UNITY LIFECYCLE

        protected virtual void Reset()
        {
            _levelEventTrigger = GetComponent<LevelEventTrigger>();
        }

        protected virtual void OnDestroy()
        {
            if (_levelEventTrigger != null)
            {
                _levelEventTrigger.OnTriggerEvent -= LevelEventTrigger_OnTriggerEvent;
            }
        }


        // EVENT HANDLING

        [ContextMenu("Test Trigger")]
        private void LevelEventTrigger_OnTriggerEvent()
        {

            TriggerEvent();

            _wasTriggered = true;
        }

        internal void SetTriggered(bool triggered)
        {
            _wasTriggered = triggered;
        }

        public abstract void TriggerEvent(bool initialization = false);
    }
}