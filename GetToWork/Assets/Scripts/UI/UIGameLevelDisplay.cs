// Copyright Isto Inc.
using Isto.Core.Localization;
using Isto.Core.UI;
using Isto.GTW.Configuration;
using System;
using TMPro;
using UnityEngine;
using Zenject;

namespace Isto.GTW.UI
{
    public class UIGameLevelDisplay : MonoBehaviour
    {

        // UNITY HOOKUP

        [SerializeField] private CoreButton _levelButton = null;
        [SerializeField] protected TextMeshProUGUI levelNumberLabel;
        [SerializeField] protected TextMeshProUGUI levelNameLabel;
        [SerializeField] protected TextMeshProUGUI totalTimeLabel;
        [SerializeField] protected TextMeshProUGUI bestTimeLabel;
        [SerializeField] protected GameObject completedCheckMark;
        [SerializeField] protected GameObject isSelectedImage;
        [SerializeField] private ColorData _greyedOutTextColor;

        // OTHER FIELDS

        private GTWGameLevelDefinition _gameLevel = null; //Set up by the parent menu
        private UISetGameModeSubState _parentMenu = null;

        // PROPERTIES
        public GTWGameLevelDefinition GameLevel => _gameLevel;

        // EVENTS


        // INJECTION
        protected ILocalizationProvider _localization;
        protected LocTerm.Factory _localizedStringFactory;

        [Inject]
        public void Inject(ILocalizationProvider localization, LocTerm.Factory localizedFactory)
        {
            _localization = localization;
            _localizedStringFactory = localizedFactory;
        }


        // EVENT HANDLING
        public void Button_OnClick()
        {
            _parentMenu.OpenLevel_ButtonClicked(_gameLevel);
        }

        public void NewButtonActive(GTWGameLevelDefinition gameLevel)
        {
            isSelectedImage.SetActive(gameLevel == _gameLevel);
        }


        // ACCESSORS
        public void Setup(GTWGameLevelDefinition _gameLvl, UISetGameModeSubState _parentMenu)
        {
            _gameLevel = _gameLvl;
            this._parentMenu = _parentMenu;


            //QUESTION - understand how this works? Also i hate the dropdowns with a fiery passion. Is there a way around this?
            LocExpression gameModeTitle = CreateLocExpressionForGameMode(_gameLevel.LevelName.mTerm, _gameLevel.fallbackLevelName);
            gameModeTitle.LocalizeInto(levelNameLabel);

            levelNumberLabel.text = _gameLevel.LevelNumber.ToString("D2");
            totalTimeLabel.text = GTWUtils.GetFormattedTimeOrDefaultString(_gameLevel.TotalTimeInLevel);
            bestTimeLabel.text = GTWUtils.GetFormattedTimeOrDefaultString(_gameLevel.FastestCompletedTime);


            completedCheckMark.SetActive(_gameLevel.IsCompleted);

            /*
            // Game Mode Description
            LocExpression gameModeDescription = CreateLocExpressionForGameMode(_gameMode.DescriptionLoc.mTerm, _gameMode.internalName);
            gameModeDescription.LocalizeInto(modeDescriptionLabel);

            LayoutRebuilder.ForceRebuildLayoutImmediate(statusLayoutGroup.transform as RectTransform);

            */
            // Set availability
            bool allowed = (_gameLevel != null && _gameLevel.IsUnlocked) || _parentMenu.AllLevelsUnlocked;
            if (_levelButton != null)
            {
                _levelButton.interactable = allowed;
            }

        }

        // OTHER METHODS


        private LocExpression CreateLocExpressionForGameMode(string localizedTerm, string fallbackTerm)
        {
            LocTerm.LocalizationType type = LocTerm.LocalizationType.Localized;
            string term = localizedTerm;

            if (string.IsNullOrEmpty(localizedTerm))
            {
                type = LocTerm.LocalizationType.NonLocalized;
                term = fallbackTerm;
            }

            LocTerm gameModeExpression = _localizedStringFactory.Create(type, term);
            return new SingleTermLocExpression(gameModeExpression);
        }
    }
}