// Copyright Isto Inc.

using Isto.GTW.Data;
using TMPro;
using UnityEngine;

namespace Isto.GTW.UI
{
    public class GTWLeaderboardPlayerDisplay : MonoBehaviour
    {
        [Header("UI References (assign in inspector)")]
        [SerializeField] private TextMeshProUGUI _rankText  = null;
        [SerializeField] private TextMeshProUGUI _nameText  = null;
        [SerializeField] private TextMeshProUGUI _scoreText = null;

        /// <summary>
        /// Call this to populate the row with data.
        /// </summary>
        public void Setup(LeaderboardEntryData data, bool isFriendsMode)
        {
            if (_rankText != null)
            {
                _rankText.text = (isFriendsMode ? data.LocalRank : data.GlobalRank).ToString();
            }

            if (_nameText  != null) _nameText.text  = data.PlayerName;
            if (_scoreText != null) _scoreText.text = data.HighestCheckpoint.ToString();
        }
    }
}