using System;
using System.Runtime.InteropServices;
using System.Collections.Generic;
using Unity.GameCore.Interop;

namespace Unity.GameCore
{
    public delegate void XPackageInstalledCallback(XPackageDetails details);
    public delegate bool XPackageChunkAvailabilityCallback(XPackageChunkSelector selector, XPackageChunkAvailability availability);
    public delegate void XPackageInstallationProgressCallback(XPackageInstallationMonitorHandle installationMonitor);
    public delegate void XPackageFeatureEnumerationCallback(XPackageFeature features);
    public delegate void XPackageMountWithUiAsyncCompleted(Int32 hresult, XPackageMountHandle mountHandle);
    public delegate void XPackageInstallChunksCompleted(Int32 hresult, XPackageInstallationMonitorHandle installationMonitor);

    partial class SDK
    {
        #region Callbacks
        [MonoPInvokeCallback]
        private static NativeBool EnumerationCallback(IntPtr context, ref Interop.XPackageDetails packageDetails)
        {
            // Context contains the collection we are enumerating
            GCHandle collectionHandle = GCHandle.FromIntPtr(context);
            var collection = collectionHandle.Target as List<XPackageDetails>;
            collection.Add(new XPackageDetails(packageDetails));

            return new NativeBool(true);
        }

        [MonoPInvokeCallback]
        private static void PackageInstalledCallback(IntPtr context, ref Interop.XPackageDetails packageDetails)
        {
            GCHandle callbackHandle = GCHandle.FromIntPtr(context);
            var callbacks = callbackHandle.Target as UnmanagedCallback<Interop.XPackageInstalledCallback, XPackageInstalledCallback>;
            callbacks.userCallback?.Invoke(new XPackageDetails(packageDetails));
        }

        [MonoPInvokeCallback]
        private static NativeBool PackageChunkAvailabilityCallback(IntPtr context, ref Interop.XPackageChunkSelector selector, XPackageChunkAvailability availability)
        {
            GCHandle callbackHandle = GCHandle.FromIntPtr(context);
            var callbacks = callbackHandle.Target as UnmanagedCallback<Interop.XPackageChunkAvailabilityCallback, XPackageChunkAvailabilityCallback>;
            if (callbacks.userCallback != null)
            {
                bool shouldContinueEnumerating = callbacks.userCallback.Invoke(new XPackageChunkSelector(selector), availability);
                // If the user callback returns true, it signals the native code to continue
                // enumerating
                return new NativeBool(shouldContinueEnumerating);
            }

            // Returning false means we are stopping the enumeration
            // no need to free the GC handle here because it will be freed once the
            // XGRInterop.XPackageEnumerateChunkAvailability call returns
            return new NativeBool(false);
        }

        [MonoPInvokeCallback]
        private static void PackageInstallationProgressCallback(IntPtr context, Interop.XPackageInstallationMonitorHandle monitor)
        {
            GCHandle callbackHandle = GCHandle.FromIntPtr(context);
            var callbacks = callbackHandle.Target as UnmanagedCallback<Interop.XPackageInstallationProgressCallback, XPackageInstallationProgressCallback>;
            callbacks.userCallback?.Invoke(new XPackageInstallationMonitorHandle(monitor));
        }

        [MonoPInvokeCallback]
        private static NativeBool FeatureEnumerationCallback(
          IntPtr context,
          ref Interop.XPackageFeature feature)
        {
            GCHandle collectionHandle = GCHandle.FromIntPtr(context);
            var collection = collectionHandle.Target as List<XPackageFeature>;
            collection.Add(new XPackageFeature(feature));

            return new NativeBool(true);
        }
        #endregion

        public static int XPACKAGE_IDENTIFIER_MAX_LENGTH = XGRInterop.XPACKAGE_IDENTIFIER_MAX_LENGTH;

        #region Packages
        public static Int32 XPackageGetCurrentProcessPackageIdentifier(out string identifier)
        {
            identifier = null;
            Byte[] buf = new Byte[XGRInterop.XPACKAGE_IDENTIFIER_MAX_LENGTH];
            Int32 hr = XGRInterop.XPackageGetCurrentProcessPackageIdentifier(new SizeT(buf.Length), buf);
            if (HR.SUCCEEDED(hr))
            {
                identifier = Converters.ByteArrayToString(buf);
            }

            return hr;
        }

        public static bool XPackageIsPackagedProcess()
        {
            return XGRInterop.XPackageIsPackagedProcess();
        }

        public static Int32 XPackageGetUserLocale(out string locale)
        {
            locale = null;
            Byte[] buf = new Byte[XGRInterop.LOCALE_NAME_MAX_LENGTH];
            Int32 hr = XGRInterop.XPackageGetUserLocale(new SizeT(buf.Length), buf);
            if (HR.SUCCEEDED(hr))
            {
                locale = Converters.ByteArrayToString(buf);
            }

            return hr;
        }

        public static Int32 XPackageEnumeratePackages(XPackageKind kind, XPackageEnumerationScope scope, out XPackageDetails[] details)
        {
            List<XPackageDetails> results = new List<XPackageDetails>();
            GCHandle resultsHandle = GCHandle.Alloc(results);

            Int32 hr = XGRInterop.XPackageEnumeratePackages(kind, scope, GCHandle.ToIntPtr(resultsHandle), EnumerationCallback);
            details = results.ToArray();

            resultsHandle.Free();
            return hr;
        }

        public static Int32 XPackageRegisterPackageInstalled(XPackageInstalledCallback callback, out XRegistrationToken token)
        {
            var callbacks = new UnmanagedCallback<Interop.XPackageInstalledCallback, XPackageInstalledCallback>
            {
                directCallback = PackageInstalledCallback,
                userCallback = callback
            };

            GCHandle callbackHandle = GCHandle.Alloc(callbacks);

            Int32 hr = XGRInterop.XPackageRegisterPackageInstalled(
                    defaultQueue.handle, 
                    GCHandle.ToIntPtr(callbackHandle),
                    callbacks.directCallback,
                    out XTaskQueueRegistrationToken taskQueueToken);

            if (HR.SUCCEEDED(hr))
            {
                token = new XRegistrationToken(callbackHandle, taskQueueToken);
            }
            else
            {
                token = default(XRegistrationToken);
                callbackHandle.Free();
            }

            return hr;
        }

        public static void XPackageUnregisterPackageInstalled(XRegistrationToken token)
        {
            if (token == null)
            {
                return;
            }

            XGRInterop.XPackageUnregisterPackageInstalled(token.Token, true);
            token.CallbackHandle.Free();
        }

        public static int XPackageEnumerateFeatures(
          string packageIdentifier,
          out XPackageFeature[] features)
        {
            List<XPackageFeature> results = new List<XPackageFeature>();
            GCHandle featuresHandle = GCHandle.Alloc(results);

            int hr = XGRInterop.XPackageEnumerateFeatures(Converters.StringToNullTerminatedUTF8ByteArray(packageIdentifier), GCHandle.ToIntPtr(featuresHandle), FeatureEnumerationCallback);
            features = results.ToArray();

            featuresHandle.Free();
            return hr;
        }
        #endregion

        #region Mounting
        [Obsolete("This method is deprecated, use XPackageMountWithUiAsync instead.", false)]
        public static Int32 XPackageMount(string packageIdentifier, out XPackageMountHandle mountHandle)
        {
            mountHandle = null;
            Interop.XPackageMountHandle mh;
            Int32 hr = XGRInterop.XPackageMount(Converters.StringToNullTerminatedUTF8ByteArray(packageIdentifier), out mh);
            if (HR.SUCCEEDED(hr))
            {
                mountHandle = new XPackageMountHandle(mh);
            }
            return hr;
        }

        public static Int32 XPackageMountWithUiAsync(string packageIdentifier, XPackageMountWithUiAsyncCompleted completionRoutine)
        {
            XAsyncBlockPtr asyncBlock = AsyncHelpers.WrapAsyncBlock(defaultQueue.handle, (XAsyncBlockPtr block) =>
            {
                Interop.XPackageMountHandle mh;
                Int32 hresult = XGRInterop.XPackageMountWithUiResult(block, out mh);
                if (HR.SUCCEEDED(hresult))
                {
                    XPackageMountHandle mountHandle = new XPackageMountHandle(mh);
                    completionRoutine(hresult, mountHandle);
                }
                else
                {
                    completionRoutine(hresult, null);
                }
            });

            Int32 hr = XGRInterop.XPackageMountWithUiAsync(Converters.StringToNullTerminatedUTF8ByteArray(packageIdentifier), asyncBlock);

            if (HR.FAILED(hr))
            {
                AsyncHelpers.CleanupAsyncBlock(asyncBlock);
                completionRoutine(hr, null);
            }

            return hr;
        }

        public static Int32 XPackageGetMountPath(XPackageMountHandle mountHandle, out string path)
        {
            path = string.Empty;

            if (mountHandle == null)
            {
                return HR.E_INVALIDARG;
            }

            SizeT size;
            Int32 hr = XGRInterop.XPackageGetMountPathSize(mountHandle.Handle, out size);
            if (HR.FAILED(hr))
            {
                return hr;
            }

            Byte[] buf = new Byte[size.ToInt32()];
            hr = XGRInterop.XPackageGetMountPath(mountHandle.Handle, size, buf);
            if (HR.SUCCEEDED(hr))
            {
                path = Converters.ByteArrayToString(buf);
            }

            return hr;
        }

        public static void XPackageCloseMountHandle(XPackageMountHandle mountHandle)
        {
            if (mountHandle == null)
            {
                return;
            }

            XGRInterop.XPackageCloseMountHandle(mountHandle.Handle);
            mountHandle.Handle = new Interop.XPackageMountHandle { handle = IntPtr.Zero };
        }
        #endregion

        #region Install Monitor
        public static Int32 XPackageCreateInstallationMonitor(
            string packageIdentifier,
            UInt32 minimumUpdateIntervalMs,
            out XPackageInstallationMonitorHandle installationMonitor)
        {
            return XPackageCreateInstallationMonitor(
                packageIdentifier,
                null,
                minimumUpdateIntervalMs,
                out installationMonitor);
        }

        public static Int32 XPackageCreateInstallationMonitor(
            string packageIdentifier,
            XPackageChunkSelector[] selectors,
            UInt32 minimumUpdateIntervalMs,
            out XPackageInstallationMonitorHandle installationMonitor)
        {
            UInt32 selectorsCount = 0;
            Interop.XPackageChunkSelector[] selectorsInterop = null;
            DisposableCollection disposableCollection = null; // need to keep a reference of this until after the Interop call

            if (selectors != null)
            {
                disposableCollection = new DisposableCollection();
                selectorsInterop = Converters.ConvertArrayToFixedLength(selectors, selectors.Length, r => new Interop.XPackageChunkSelector(r, disposableCollection));
                selectorsCount = (UInt32) selectors.Length;
            }

            int hr = XGRInterop.XPackageCreateInstallationMonitor(
                Converters.StringToNullTerminatedUTF8ByteArray(packageIdentifier),
                selectorsCount,
                selectorsInterop,
                minimumUpdateIntervalMs,
                defaultQueue.handle,
                out Interop.XPackageInstallationMonitorHandle interopHandle);

            return XPackageInstallationMonitorHandle.WrapInteropHandleAndReturnHResult(hr, interopHandle, out installationMonitor);
        }

        public static void XPackageCloseInstallationMonitorHandle(XPackageInstallationMonitorHandle installationMonitor)
        {
            if (installationMonitor == null)
            {
                return;
            }

            XGRInterop.XPackageCloseInstallationMonitorHandle(installationMonitor.InteropHandle);
            installationMonitor.InteropHandle = new Interop.XPackageInstallationMonitorHandle { handle = IntPtr.Zero };
        }

        public static void XPackageGetInstallationProgress(
            XPackageInstallationMonitorHandle installationMonitor,
            out XPackageInstallationProgress installationProgress)
        {
            if (installationMonitor == null)
            {
                installationProgress = default(XPackageInstallationProgress);
                return;
            }

            XGRInterop.XPackageGetInstallationProgress(installationMonitor.InteropHandle, out Interop.XPackageInstallationProgress progress);
            installationProgress = new XPackageInstallationProgress(progress);
        }

        public static bool XPackageUpdateInstallationMonitor(XPackageInstallationMonitorHandle installationMonitor)
        {
            if (installationMonitor == null)
            {
                return default(bool);
            }

            return XGRInterop.XPackageUpdateInstallationMonitor(installationMonitor.InteropHandle);
        }

        public static Int32 XPackageRegisterInstallationProgressChanged(
            XPackageInstallationMonitorHandle installationMonitor,
            XPackageInstallationProgressCallback callback,
            out XRegistrationToken token)
        {
            token = default(XRegistrationToken);

            if (installationMonitor == null)
            {
                return HR.E_INVALIDARG;
            }

            var callbacks = new UnmanagedCallback<Interop.XPackageInstallationProgressCallback, XPackageInstallationProgressCallback>
            {
                directCallback = PackageInstallationProgressCallback,
                userCallback = callback
            };

            GCHandle callbackHandle = GCHandle.Alloc(callbacks);

            Int32 hr = XGRInterop.XPackageRegisterInstallationProgressChanged(
                installationMonitor.InteropHandle,
                GCHandle.ToIntPtr(callbackHandle),
                callbacks.directCallback,
                out XTaskQueueRegistrationToken taskQueueToken);

            if (HR.SUCCEEDED(hr))
            {
                token = new XRegistrationToken(GCHandle.Alloc(callbackHandle), taskQueueToken);
            }
            else
            {
                token = default(XRegistrationToken);
                callbackHandle.Free();
            }

            return hr;
        }

        public static void XPackageUnregisterInstallationProgressChanged(
            XPackageInstallationMonitorHandle installationMonitor,
            XRegistrationToken token)
        {
            if (token == null || installationMonitor == null)
            {
                return;
            }

            XGRInterop.XPackageUnregisterInstallationProgressChanged(
                installationMonitor.InteropHandle,
                token.Token,
                wait: true
            );

            token.CallbackHandle.Free();
        }

        public static Int32 XPackageEstimateDownloadSize(
            string packageIdentifier,
            out UInt64 downloadSize,
            out bool shouldPresentUserConfirmation)
        {
            return XPackageEstimateDownloadSize(packageIdentifier, null, out downloadSize, out shouldPresentUserConfirmation);
        }

        public static Int32 XPackageEstimateDownloadSize(
            string packageIdentifier,
            XPackageChunkSelector[] selectors,
            out UInt64 downloadSize,
            out bool shouldPresentUserConfirmation)
        {
            UInt32 selectorsCount = 0;
            Interop.XPackageChunkSelector[] selectorsInterop = null;
            DisposableCollection disposableCollection = null; // need to keep a reference of this until after the Interop call

            if (selectors != null)
            {
                disposableCollection = new DisposableCollection();
                selectorsInterop = Converters.ConvertArrayToFixedLength(selectors, selectors.Length, r => new Interop.XPackageChunkSelector(r, disposableCollection));
                selectorsCount = (UInt32)selectors.Length;
            }

            return XGRInterop.XPackageEstimateDownloadSize(
                Converters.StringToNullTerminatedUTF8ByteArray(packageIdentifier),
                selectorsCount,
                selectorsInterop, 
                out downloadSize,
                out shouldPresentUserConfirmation);
        }

        public static Int32 XPackageFindChunkAvailability(
            string packageIdentifier,
            XPackageChunkSelector[] selectors,
            out XPackageChunkAvailability availability)
        {
            UInt32 selectorsCount = 0;
            Interop.XPackageChunkSelector[] selectorsInterop = null;
            DisposableCollection disposableCollection = null; // need to keep a reference of this until after the Interop call

            if (selectors != null)
            {
                disposableCollection = new DisposableCollection();
                selectorsInterop = Converters.ConvertArrayToFixedLength(selectors, selectors.Length, r => new Interop.XPackageChunkSelector(r, disposableCollection));
                selectorsCount = (UInt32)selectors.Length;
            }

            return XGRInterop.XPackageFindChunkAvailability(
                Converters.StringToNullTerminatedUTF8ByteArray(packageIdentifier),
                selectorsCount,
                selectorsInterop,
                out availability);
        }

        public static Int32 XPackageEnumerateChunkAvailability(
            string packageIdentifier,
            XPackageChunkSelectorType type,
            XPackageChunkAvailabilityCallback callback)
        {
            var callbacks = new UnmanagedCallback<Interop.XPackageChunkAvailabilityCallback, XPackageChunkAvailabilityCallback>
            {
                directCallback = PackageChunkAvailabilityCallback,
                userCallback = callback
            };

            GCHandle callbackHandle = GCHandle.Alloc(callbacks);

            // This API works a bit differently, it is blocking and all calls to the callback
            // happen inside the native call. Once the following call returns, no more invocations
            // of the callback will occur, so at that point we can safely free the GC handle.
            Int32 hr = XGRInterop.XPackageEnumerateChunkAvailability(
                Converters.StringToNullTerminatedUTF8ByteArray(packageIdentifier),
                type,
                GCHandle.ToIntPtr(callbackHandle),
                callbacks.directCallback);

            callbackHandle.Free();

            return hr;
        }

        public static Int32 XPackageChangeChunkInstallOrder(
            string packageIdentifier,
            XPackageChunkSelector[] selectors
            )
        {
            UInt32 selectorsCount = 0;
            Interop.XPackageChunkSelector[] selectorsInterop = null;
            DisposableCollection disposableCollection = null; // need to keep a reference of this until after the Interop call

            if (selectors != null)
            {
                disposableCollection = new DisposableCollection();
                selectorsInterop = Converters.ConvertArrayToFixedLength(selectors, selectors.Length, r => new Interop.XPackageChunkSelector(r, disposableCollection));
                selectorsCount = (UInt32)selectors.Length;
            }

            return XGRInterop.XPackageChangeChunkInstallOrder(
                Converters.StringToNullTerminatedUTF8ByteArray(packageIdentifier),
                selectorsCount,
                selectorsInterop);
        }

        public static Int32 XPackageInstallChunksAsync(
            string packageIdentifier,
            XPackageChunkSelector[] selectors,
            UInt32 minimumUpdateIntervalMs,
            bool suppressUserConfirmation,
            XPackageInstallChunksCompleted completionRoutine)
        {
            XAsyncBlockPtr asyncBlock = AsyncHelpers.WrapAsyncBlock(defaultQueue.handle, (XAsyncBlockPtr block) =>
            {
                Interop.XPackageInstallationMonitorHandle im;
                Int32 hresult = XGRInterop.XPackageInstallChunksResult(block, out im);
                if (HR.SUCCEEDED(hresult))
                {
                    XPackageInstallationMonitorHandle installationMonitor = new XPackageInstallationMonitorHandle(im);
                    completionRoutine(hresult, installationMonitor);
                }
                else
                {
                    completionRoutine(hresult, null);
                }
            });

            UInt32 selectorsCount = 0;
            Interop.XPackageChunkSelector[] selectorsInterop = null;
            DisposableCollection disposableCollection = null; // need to keep a reference of this until after the Interop call

            if (selectors != null)
            {
                disposableCollection = new DisposableCollection();
                selectorsInterop = Converters.ConvertArrayToFixedLength(selectors, selectors.Length, r => new Interop.XPackageChunkSelector(r, disposableCollection));
                selectorsCount = (UInt32)selectors.Length;
            }

            Int32 hr = XGRInterop.XPackageInstallChunksAsync(
                Converters.StringToNullTerminatedUTF8ByteArray(packageIdentifier),
                selectorsCount,
                selectorsInterop,
                minimumUpdateIntervalMs,
                suppressUserConfirmation,
                asyncBlock);

            if (HR.FAILED(hr))
            {
                AsyncHelpers.CleanupAsyncBlock(asyncBlock);
                completionRoutine(hr, null);
            }

            return hr;
        }

        public static Int32 XPackageUninstallChunks(
            string packageIdentifier,
            XPackageChunkSelector[] selectors
            )
        {
            UInt32 selectorsCount = 0;
            Interop.XPackageChunkSelector[] selectorsInterop = null;
            DisposableCollection disposableCollection = null; // need to keep a reference of this until after the Interop call

            if (selectors != null)
            {
                disposableCollection = new DisposableCollection();
                selectorsInterop = Converters.ConvertArrayToFixedLength(selectors, selectors.Length, r => new Interop.XPackageChunkSelector(r, disposableCollection));
                selectorsCount = (UInt32)selectors.Length;
            }

            return XGRInterop.XPackageUninstallChunks(
                Converters.StringToNullTerminatedUTF8ByteArray(packageIdentifier),
                selectorsCount,
                selectorsInterop);
        }

        public static Int32 XPackageGetWriteStats(out XPackageWriteStats writeStats)
        {
            Interop.XPackageWriteStats writeStatsInterop;

            int hr = XGRInterop.XPackageGetWriteStats(out writeStatsInterop);
            writeStats = new XPackageWriteStats(writeStatsInterop);

            return hr;
        }
        #endregion
    }
}
