using System;
using System.Collections;
using System.Collections.Generic;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Unity.GameCore;

public class XNetworkingTests
{
    // https://developer.microsoft.com/en-us/games/xbox/docs/gdk/xnetworkingquerypreferredlocaludpmultiplayerport
    [Test]
    public void XNetworkingQueryPreferredLocalUdpMultiplayerPort()
    {
        UInt16 preferredLocalUdpMultiplayerPort = 0;
        int hResult = SDK.XNetworkingQueryPreferredLocalUdpMultiplayerPort(out preferredLocalUdpMultiplayerPort);
        if (HR.SUCCEEDED(hResult))
        {
            Assert.NotZero(preferredLocalUdpMultiplayerPort, "Expected none zero value for multiplayer port");
            return;
        }
        
        Assert.Fail($"Call to XNetworkingQueryPreferredLocalUdpMultiplayerPort() failed! (hResult=0x{hResult:X8} '{HR.NameOf(hResult)}')");
    }

    //https://developer.microsoft.com/en-us/games/xbox/docs/gdk/xnetworkinggetconnectivityhint
    [Test]
    public void XNetworkingGetConnectivityHint()
    {
        XNetworkingConnectivityHint connectivityHint;
        int hResult = SDK.XNetworkingGetConnectivityHint(out connectivityHint);
        if (HR.SUCCEEDED(hResult))
        {
            return;
        }
        
        Assert.Fail($"Call to XNetworkingGetConnectivityHint() failed! (hResult=0x{hResult:X8} '{HR.NameOf(hResult)}')");
    }
}
